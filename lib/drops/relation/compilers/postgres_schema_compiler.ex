defmodule Drops.Relation.Compilers.PostgresSchemaCompiler do
  @moduledoc """
  PostgreSQL-specific schema compiler for converting SQL Database structures to Relation Schema structures.

  This module implements the `Drops.Relation.Compilers.SchemaCompiler` behavior to provide PostgreSQL-specific
  type mapping and field processing. It converts PostgreSQL database types to Ecto types
  and handles PostgreSQL-specific type characteristics.

  ## PostgreSQL Type System

  PostgreSQL has a sophisticated type system that this compiler maps to Ecto types:

  ### Integer Types
  - `integer`, `int`, `int4`, `bigint`, `int8`, `smallint`, `int2` → `:integer`
  - Serial types (`serial`, `bigserial`, `smallserial`) → `:integer`
  - Primary key integers → `:id`
  - Foreign key integers → `:id`

  ### UUID Types
  - `uuid` → `:binary` (general use)
  - Primary key UUIDs → `:binary_id`
  - Foreign key UUIDs → `:binary_id`

  ### Array Types
  - `integer[]`, `text[]`, etc. → `{:array, base_type}`
  - Recursive processing for nested arrays

  ### Other Types
  - Text types (`text`, `varchar`, etc.) → `:string`
  - Floating point types → `:float`
  - Decimal types → `:decimal`
  - Boolean type → `:boolean`
  - Date/time types → `:date`, `:time`, `:naive_datetime`, `:utc_datetime`
  - JSON types → `:map`

  ## Usage

  This compiler is typically used automatically by database introspection:

      # Automatic usage through database introspection
      {:ok, table} = Drops.SQL.Database.table("users", MyRepo)
      schema = Drops.Relation.Compilers.PostgresSchemaCompiler.process(table, [])

      # Direct usage (advanced)
      schema = Drops.Relation.Compilers.PostgresSchemaCompiler.visit(table, [])

  ## Implementation Notes

  - Handles PostgreSQL's internal type names (e.g., `int4` → `integer`)
  - Supports array type detection and recursive processing
  - Maps primary key and foreign key fields to appropriate ID types
  - Preserves PostgreSQL-specific type information where possible
  - Uses metadata to determine appropriate Ecto types for special cases
  """

  use Drops.Relation.Compilers.SchemaCompiler

  alias Drops.Relation.Schema.Field
  alias Drops.SQL.Database

  @doc """
  Visits a column struct and converts PostgreSQL types to Ecto types.

  This function implements PostgreSQL-specific type mapping, handling PostgreSQL's
  rich type system including arrays, UUIDs, and special ID type handling for
  primary keys and foreign keys.

  ## Parameters

  - `column` - A Drops.SQL.Database.Column struct with PostgreSQL type information
  - `opts` - Processing options including table context

  ## Returns

  A Drops.Relation.Schema.Field struct with appropriate Ecto type.

  ## Examples

      iex> column = %Database.Column{type: :integer, meta: %{primary_key: true}}
      iex> field = PostgresSchemaCompiler.visit(column, table: table)
      iex> field.type
      :id

      iex> column = %Database.Column{type: :uuid, meta: %{foreign_key: true}}
      iex> field = PostgresSchemaCompiler.visit(column, table: table)
      iex> field.type
      :binary_id

      iex> column = %Database.Column{type: {:array, :string}}
      iex> field = PostgresSchemaCompiler.visit(column, table: table)
      iex> field.type
      {:array, :string}
  """
  @spec visit(Database.Column.t(), keyword()) :: Field.t()
  def visit(%Database.Column{} = column, opts) do
    components = [:name, :type, :meta]

    result =
      Enum.reduce(components, %{}, fn key, acc ->
        Map.put(acc, key, visit(Map.get(column, key), Keyword.put(opts, :meta, column.meta)))
      end)

    Field.new(result.name, result.type, result.meta)
  end

  # Visit individual column components
  def visit(name, _opts) when is_atom(name), do: name

  def visit(type, opts) when is_atom(type) or is_tuple(type) do
    # Get the column from opts to apply type conversion
    column = %Database.Column{
      type: type,
      meta: Keyword.get(opts, :meta, %{})
    }

    convert_postgres_type(column)
  end

  def visit(meta, opts) when is_map(meta) do
    # Get the column from opts to access name and type
    column_meta = Keyword.get(opts, :meta, %{})

    %{
      type: column_meta.type || meta.type,
      source: meta.name,
      primary_key: meta.primary_key,
      foreign_key: meta.foreign_key,
      nullable: meta.nullable,
      default: meta.default,
      check_constraints: meta.check_constraints
    }
  end

  # Private function to convert PostgreSQL types to Ecto types
  # This implements the same logic as Types.Postgres.to_ecto_type/1
  defp convert_postgres_type(%Database.Column{type: :integer, meta: %{primary_key: true}}),
    do: :id

  defp convert_postgres_type(%Database.Column{type: :integer, meta: %{foreign_key: true}}),
    do: :id

  defp convert_postgres_type(%Database.Column{type: :uuid, meta: %{primary_key: true}}),
    do: :binary_id

  defp convert_postgres_type(%Database.Column{type: :uuid, meta: %{foreign_key: true}}),
    do: :binary_id

  defp convert_postgres_type(%Database.Column{type: :uuid}), do: :binary

  defp convert_postgres_type(%Database.Column{type: {:array, member}} = column) do
    # Create a temporary column struct for the member type to process it correctly
    member_column = %{column | type: member}
    member_ecto_type = convert_postgres_type(member_column)
    {:array, member_ecto_type}
  end

  defp convert_postgres_type(%Database.Column{type: type}), do: type
end
