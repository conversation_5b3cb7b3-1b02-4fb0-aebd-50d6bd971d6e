defmodule Drops.Relation.Compilers.SqliteSchemaCompiler do
  @moduledoc """
  SQLite-specific schema compiler for converting SQL Database structures to Relation Schema structures.

  This module implements the `Drops.Relation.Compilers.SchemaCompiler` behavior to provide SQLite-specific
  type mapping and field processing. It converts SQLite database types to Ecto types
  and handles SQLite-specific type characteristics.

  ## SQLite Type System

  SQLite uses a dynamic type system with type affinity rather than strict types.
  This compiler maps SQLite's type affinities to appropriate Ecto types:

  ### Numeric Types
  - `integer` → `:integer`
  - `real`, `float` → `:float`
  - `numeric`, `decimal` → `:decimal`

  ### Text Types
  - `string` → `:string`
  - Character types (VARCHAR, CHAR, etc.) → `:string`

  ### Binary Types
  - `binary` → `:binary`

  ### Boolean Types
  - `boolean`, `bool` → `:boolean`
  - Integer columns with boolean defaults (true/false) → `:boolean`

  ### Date/Time Types
  - `date` → `:date`
  - `time` → `:time`
  - `datetime`, `timestamp` → `:naive_datetime`

  ### Special Types
  - `uuid` → `:binary_id` (SQLite stores UUIDs as binary)
  - `json` → `:map`

  ## Type Affinity Rules

  SQLite's type affinity rules are respected:
  - Types containing "INT" are mapped to `:integer`
  - Types containing "CHAR", "CLOB", or "TEXT" are mapped to `:string`
  - Types containing "BLOB" or no affinity are mapped to `:binary`
  - Types containing "REAL", "FLOA", or "DOUB" are mapped to `:float`

  ## Usage

  This compiler is typically used automatically by database introspection:

      # Automatic usage through database introspection
      {:ok, table} = Drops.SQL.Database.table("users", MyRepo)
      schema = Drops.Relation.Compilers.SqliteSchemaCompiler.process(table, [])

      # Direct usage (advanced)
      schema = Drops.Relation.Compilers.SqliteSchemaCompiler.visit(table, [])

  ## Implementation Notes

  - Handles SQLite's dynamic typing system
  - Detects boolean types from integer columns with boolean default values
  - Maps UUID types to `:binary_id` for SQLite compatibility
  - Preserves SQLite-specific type information where possible
  - Uses metadata to determine appropriate Ecto types for special cases
  """

  use Drops.Relation.Compilers.SchemaCompiler

  alias Drops.Relation.Schema.Field
  alias Drops.SQL.Database

  @doc """
  Visits a column struct and converts SQLite types to Ecto types.

  This function implements SQLite-specific type mapping, handling SQLite's
  dynamic type system and special cases like boolean detection from integer
  columns with boolean default values.

  ## Parameters

  - `column` - A Drops.SQL.Database.Column struct with SQLite type information
  - `opts` - Processing options including table context

  ## Returns

  A Drops.Relation.Schema.Field struct with appropriate Ecto type.

  ## Examples

      iex> column = %Database.Column{type: :integer, meta: %{default: true}}
      iex> field = SqliteSchemaCompiler.visit(column, table: table)
      iex> field.type
      :boolean

      iex> column = %Database.Column{type: :uuid}
      iex> field = SqliteSchemaCompiler.visit(column, table: table)
      iex> field.type
      :binary_id

      iex> column = %Database.Column{type: :string}
      iex> field = SqliteSchemaCompiler.visit(column, table: table)
      iex> field.type
      :string
  """
  @spec visit(Database.Column.t(), keyword()) :: Field.t()
  def visit(%Database.Column{} = column, _opts) do
    ecto_type = convert_sqlite_type(column)

    meta = %{
      type: column.type,
      source: column.name,
      primary_key: column.meta.primary_key,
      foreign_key: column.meta.foreign_key,
      nullable: column.meta.nullable,
      default: column.meta.default,
      check_constraints: column.meta.check_constraints
    }

    Field.new(column.name, ecto_type, meta)
  end

  # Private function to convert SQLite types to Ecto types
  # This implements the same logic as Types.Sqlite.to_ecto_type/1
  defp convert_sqlite_type(%Database.Column{type: :integer, meta: %{default: default}})
       when default in [true, false] do
    :boolean
  end

  defp convert_sqlite_type(%Database.Column{type: :uuid}), do: :binary_id

  defp convert_sqlite_type(%Database.Column{type: type}), do: type
end
